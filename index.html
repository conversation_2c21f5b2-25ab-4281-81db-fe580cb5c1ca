<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VoiceA - Voice Recording App</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        
        .status {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .shortcut-info {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-size: 0.9em;
        }
        
        .key {
            background: rgba(255,255,255,0.2);
            padding: 5px 10px;
            border-radius: 5px;
            font-family: monospace;
            font-weight: bold;
        }
        
        .instructions {
            text-align: left;
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #ffd700;
        }
        
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 8px 0;
            line-height: 1.5;
        }
        
        .api-status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .api-status.success {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid rgba(76, 175, 80, 0.5);
        }
        
        .api-status.error {
            background: rgba(244, 67, 54, 0.3);
            border: 1px solid rgba(244, 67, 54, 0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 VoiceA</h1>
        <div class="subtitle">Voice Recording with Global Shortcuts</div>
        
        <div class="status">
            <h3>Status</h3>
            <p>App is running in the background</p>
            <p>Press <span class="key">Ctrl+Alt</span> and hold to start recording</p>
        </div>
        
        <div class="shortcut-info">
            <h3>Global Shortcut</h3>
            <p>Hold <span class="key">Ctrl+Alt</span> to record</p>
            <p>Release to stop and transcribe</p>
        </div>
        
        <div class="instructions">
            <h3>How it works:</h3>
            <ol>
                <li>Hold <span class="key">Ctrl+Alt</span> to start recording</li>
                <li>A transparent widget appears at the top-right with waveform</li>
                <li>Speak your message while holding the keys</li>
                <li>Release <span class="key">Ctrl+Alt</span> to stop recording</li>
                <li>Audio is automatically transcribed using OpenAI Whisper</li>
                <li>Transcribed text is pasted at your cursor position</li>
                <li>Widget auto-hides after completion</li>
            </ol>
        </div>
        

        
        <div class="manual-controls" id="manualControls">
            <h3>🎤 VoiceA Controls</h3>
            <p>Use these buttons to record:</p>
            <button id="startRecording" style="background: #4CAF50; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 5px; cursor: pointer; font-size: 14px;">Start Recording</button>
            <button id="stopRecording" style="background: #f44336; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 5px; cursor: pointer; display: none; font-size: 14px;">Stop Recording</button>
            <button id="quitApp" style="background: #666; color: white; border: none; padding: 8px 16px; border-radius: 5px; margin: 5px; cursor: pointer; font-size: 12px;">Quit App</button>
        </div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');
        
        // Show manual controls if global shortcuts fail
        ipcRenderer.on('show-manual-controls', () => {
            document.getElementById('manualControls').style.display = 'block';
        });
        
        // Manual recording controls
        document.getElementById('startRecording').addEventListener('click', () => {
            ipcRenderer.send('manual-start-recording');
            document.getElementById('startRecording').style.display = 'none';
            document.getElementById('stopRecording').style.display = 'inline-block';
        });
        
        document.getElementById('stopRecording').addEventListener('click', () => {
            ipcRenderer.send('manual-stop-recording');
            document.getElementById('startRecording').style.display = 'inline-block';
            document.getElementById('stopRecording').style.display = 'none';
        });
        
        document.getElementById('quitApp').addEventListener('click', () => {
            ipcRenderer.send('quit-app');
        });
    </script>
</body>
</html> 