# VoiceA Login System Features

## Overview
VoiceA now includes a comprehensive login system with persistent authentication, automatic login, and token usage tracking.

## Features Implemented

### 1. Login System
- **Login Form**: Clean, modern login interface on startup
- **API Integration**: Uses the provided DummyJSON API for authentication
- **Remember Me**: Option to save credentials for automatic login
- **Auto-Login**: Automatically logs in on app restart if credentials are saved
- **Secure Storage**: Credentials are encrypted and stored locally
- **Authentication Required**: All recording and transcription features require login

### 2. Persistent Authentication
- **Encrypted Storage**: User credentials are encrypted using AES-256 encryption
- **Token Management**: JWT tokens are stored and validated
- **Auto-Refresh**: Automatically attempts to refresh expired tokens
- **Secure Cleanup**: Credentials are securely removed on logout

### 3. User Interface
- **Welcome Bar**: Shows logged-in user information
- **Plan Details**: Dedicated window showing user plan and token usage
- **Logout Option**: Easy logout with credential cleanup
- **Status Indicators**: Visual feedback for login status

### 4. Token Tracking
- **Usage Monitoring**: Tracks Whisper API token consumption
- **Statistics**: Daily, weekly, and monthly usage statistics
- **External API**: Sends token usage data to external tracking API
- **Local Storage**: Maintains local usage history

### 5. Auto-Start Integration
- **Service Check**: Verifies user login status on app startup
- **Background Operation**: Continues to work in system tray
- **Authentication Gate**: Recording features only work after successful login

### 6. Security Controls
- **Shortcut Protection**: Global shortcuts only work when user is authenticated
- **Transcription Gate**: Whisper API calls require valid user session
- **Visual Indicators**: Status dot changes color based on authentication state
- **Session Validation**: Tokens are verified before allowing operations

## File Structure

### New Files Added:
- `auth-service.js` - Authentication service with encryption
- `token-tracker.js` - Token usage tracking and statistics
- `user-plan.html` - User plan details window
- `LOGIN_FEATURES.md` - This documentation file

### Modified Files:
- `main.html` - Added login form and user interface
- `main.js` - Integrated authentication and token tracking services

## API Endpoints Used

### Authentication API (DummyJSON)
```javascript
// Login
POST https://dummyjson.com/auth/login
{
  "username": "emilys",
  "password": "emilyspass",
  "expiresInMins": 30
}

// Verify Token
GET https://dummyjson.com/auth/me
Headers: { "Authorization": "Bearer <token>" }
```

### Token Tracking API (Configurable)
```javascript
// Track Usage
POST https://your-api-endpoint.com/api/track-tokens
{
  "userId": "user_id",
  "username": "username",
  "tokensUsed": 150,
  "timestamp": "2025-01-01T00:00:00.000Z",
  "service": "whisper"
}
```

## Security Features

### Encryption
- **AES-256**: Strong encryption for stored credentials
- **Unique Keys**: Each installation generates a unique encryption key
- **Secure Storage**: Credentials stored in user data directory

### Token Management
- **JWT Validation**: Tokens are validated before use
- **Automatic Cleanup**: Invalid tokens are automatically removed
- **Secure Transmission**: All API calls use HTTPS

## Usage Instructions

### First Time Setup
1. Start the application
2. Enter your username and password
3. Check "Remember me" to enable auto-login
4. Click "Login"

### Authentication Behavior
- **Before Login**: All recording features are disabled
  - Global shortcuts (Ctrl+Alt) are blocked
  - Test Recording button shows login prompt
  - Recording Widget button shows login prompt
  - Status indicator shows red dot with "Please log in" message

- **After Login**: All features are enabled
  - Global shortcuts work normally
  - All recording and transcription features available
  - Status indicator shows green blinking dot
  - User information displayed in welcome bar

### Automatic Login
- If "Remember me" was checked, the app will automatically log in on restart
- Invalid credentials are automatically cleared
- Manual login is required if auto-login fails

### Viewing Plan Details
1. Click "Plan Details" in the user info bar
2. View token usage statistics
3. See user information and plan features

### Logout
1. Click "Logout" in the user info bar
2. Credentials are securely removed
3. Return to login screen

## Token Usage Tracking

### Automatic Tracking
- Every Whisper API call is automatically tracked
- Token usage is estimated based on text length
- Statistics are updated in real-time

### External Reporting
- Token usage is sent to external API (configurable)
- Includes user information and usage context
- Handles API failures gracefully

### Local Statistics
- Daily, weekly, and monthly usage totals
- Session history (last 1000 sessions)
- Persistent storage across app restarts

## Configuration

### API Endpoints
To change the token tracking API endpoint, modify the `sendToExternalAPI` function in `token-tracker.js`:

```javascript
const apiEndpoint = 'https://your-api-endpoint.com/api/track-tokens';
```

### Encryption
The encryption key is automatically generated and stored in the user data directory. No manual configuration is required.

## Troubleshooting

### Login Issues
- Verify internet connection
- Check username/password combination
- Clear saved credentials if auto-login fails

### Token Tracking
- Check console for API errors
- Verify external API endpoint is accessible
- Local tracking continues even if external API fails

### Auto-Start
- Ensure app has permission to start with system
- Check user data directory permissions
- Verify encryption key file exists

## Development Notes

### Testing Credentials
For testing with DummyJSON API, use:
- Username: `emilys`
- Password: `emilyspass`

### File Locations
- User data: `%APPDATA%/voicea/` (Windows)
- Auth data: `%APPDATA%/voicea/auth/user-auth.json`
- Token data: `%APPDATA%/voicea/tokens/usage.json`
- Encryption key: `%APPDATA%/voicea/app.key`

## Future Enhancements

### Planned Features
- Multiple user account support
- Cloud synchronization of token usage
- Advanced usage analytics
- Plan upgrade notifications
- Offline mode with sync

### API Improvements
- Refresh token implementation
- Better error handling
- Rate limiting support
- Batch token reporting
