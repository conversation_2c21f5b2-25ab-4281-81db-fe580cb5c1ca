@echo off
echo Easy Voice - Test Installation Check
echo ===================================
echo.
echo This script tests the pre-installation check functionality.
echo It will detect if Easy Voice is running and demonstrate
echo the installation process handling.
echo.
pause

echo.
echo Running pre-installation check...
echo ---------------------------------
powershell.exe -ExecutionPolicy Bypass -File "installer\pre-install-check.ps1"

echo.
echo Check completed with exit code: %ERRORLEVEL%
echo.
if %ERRORLEVEL%==0 (
    echo ✅ SUCCESS: Installation can proceed safely
    echo No Easy Voice processes are running.
) else (
    echo ❌ BLOCKED: Installation cannot proceed
    echo Easy Voice processes are still running or user cancelled.
)

echo.
echo This is how the installer will behave:
echo - Exit code 0 = Installation proceeds
echo - Exit code 1 = Installation is blocked/cancelled
echo.
pause
