#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Easy Voice Build Script');
console.log('==========================');

// Check if we're on the right platform
const platform = process.platform;
console.log(`📱 Platform: ${platform}`);

// Create dist directory if it doesn't exist
const distDir = path.join(__dirname, '..', 'dist');
if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
}

// Build functions
function buildWindows() {
    console.log('🪟 Building for Windows...');
    try {
        execSync('npm run build:win', { stdio: 'inherit' });
        console.log('✅ Windows build completed!');
        
        // List generated files
        const files = fs.readdirSync(distDir).filter(f => f.endsWith('.exe'));
        console.log('📦 Generated files:');
        files.forEach(file => console.log(`   - ${file}`));
        
    } catch (error) {
        console.error('❌ Windows build failed:', error.message);
        process.exit(1);
    }
}

function buildMac() {
    console.log('🍎 Building for macOS...');
    try {
        execSync('npm run build:mac', { stdio: 'inherit' });
        console.log('✅ macOS build completed!');
        
        // List generated files
        const files = fs.readdirSync(distDir).filter(f => f.endsWith('.dmg'));
        console.log('📦 Generated files:');
        files.forEach(file => console.log(`   - ${file}`));
        
    } catch (error) {
        console.error('❌ macOS build failed:', error.message);
        console.error('💡 Note: macOS builds can only be created on macOS machines');
        process.exit(1);
    }
}

function buildLinux() {
    console.log('🐧 Building for Linux...');
    try {
        execSync('npm run build:linux', { stdio: 'inherit' });
        console.log('✅ Linux build completed!');
        
        // List generated files
        const files = fs.readdirSync(distDir).filter(f => f.endsWith('.AppImage'));
        console.log('📦 Generated files:');
        files.forEach(file => console.log(`   - ${file}`));
        
    } catch (error) {
        console.error('❌ Linux build failed:', error.message);
        process.exit(1);
    }
}

// Parse command line arguments
const args = process.argv.slice(2);
const target = args[0] || 'current';

console.log(`🎯 Target: ${target}`);
console.log('');

switch (target) {
    case 'windows':
    case 'win':
        buildWindows();
        break;
        
    case 'mac':
    case 'macos':
        if (platform !== 'darwin') {
            console.error('❌ macOS builds can only be created on macOS machines');
            console.log('💡 Use GitHub Actions or a Mac machine to build for macOS');
            process.exit(1);
        }
        buildMac();
        break;
        
    case 'linux':
        buildLinux();
        break;
        
    case 'all':
        if (platform === 'win32') {
            console.log('🪟 Building Windows (native platform)');
            buildWindows();
            console.log('');
            console.log('⚠️  Cannot build macOS on Windows');
            console.log('💡 Use GitHub Actions for cross-platform builds');
        } else if (platform === 'darwin') {
            console.log('🍎 Building macOS (native platform)');
            buildMac();
            console.log('');
            console.log('🪟 Building Windows (cross-platform)');
            buildWindows();
        } else {
            console.log('🐧 Building Linux (native platform)');
            buildLinux();
            console.log('');
            console.log('🪟 Building Windows (cross-platform)');
            buildWindows();
        }
        break;
        
    case 'current':
    default:
        if (platform === 'win32') {
            buildWindows();
        } else if (platform === 'darwin') {
            buildMac();
        } else {
            buildLinux();
        }
        break;
}

console.log('');
console.log('🎉 Build process completed!');
console.log('📁 Check the dist/ folder for your builds');
console.log('');
console.log('💡 For cross-platform builds:');
console.log('   • GitHub Actions: Push with tags (v1.0.0)');
console.log('   • Bitbucket Pipelines: Use npm run build:bitbucket');
console.log('   • Manual Mac builds: Requires macOS machine');
