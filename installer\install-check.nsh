; Custom installation check script for Easy Voice
; This script runs before installation to handle running processes

!macro customInstall
  ; Run pre-installation check
  DetailPrint "Checking for running Easy Voice processes..."

  ; Extract the PowerShell script to temp directory for access
  SetOutPath "$TEMP\EasyVoiceInstaller"
  File "installer\pre-install-check.ps1"

  ; First, try silent check to see if processes are running
  nsExec::ExecToStack 'powershell.exe -ExecutionPolicy Bypass -WindowStyle Hidden -File "$TEMP\EasyVoiceInstaller\pre-install-check.ps1" -Silent'
  Pop $0 ; Exit code
  Pop $1 ; Output

  ; If exit code is 0, no processes running - proceed
  StrCmp $0 "0" no_processes_running

  ; Processes are running - show user dialog
  MessageBox MB_YESNO|MB_ICONQUESTION "Easy Voice is currently running and must be closed before installation can continue.$\n$\nWould you like to close Easy Voice and continue with the installation?" IDYES close_and_continue IDNO cancel_install

  cancel_install:
    MessageBox MB_OK|MB_ICONINFORMATION "Installation cancelled. Please close Easy Voice manually and run the installer again."
    ; Clean up temp files
    RMDir /r "$TEMP\EasyVoiceInstaller"
    Abort

  close_and_continue:
    DetailPrint "User chose to close Easy Voice and continue..."

    ; Run the check script with force close option
    DetailPrint "Closing Easy Voice processes..."
    nsExec::ExecToLog 'powershell.exe -ExecutionPolicy Bypass -WindowStyle Hidden -File "$TEMP\EasyVoiceInstaller\pre-install-check.ps1" -ForceClose'
    Pop $0 ; Exit code

    ; Check if processes were successfully closed
    StrCmp $0 "0" processes_closed

    ; If processes couldn't be closed, show error and abort
    MessageBox MB_OK|MB_ICONERROR "Could not close Easy Voice processes. Please close the application manually and try again.$\n$\nIf the problem persists, restart your computer and run the installer again."
    ; Clean up temp files
    RMDir /r "$TEMP\EasyVoiceInstaller"
    Abort

  processes_closed:
    DetailPrint "Easy Voice processes closed successfully."
    Sleep 2000 ; Wait longer for file handles to be released
    Goto no_processes_running

  no_processes_running:
    DetailPrint "No running processes detected. Proceeding with installation..."
    ; Clean up temp files
    RMDir /r "$TEMP\EasyVoiceInstaller"
!macroend

; Custom function to check before installation starts
!macro customInit
  ; Check if we're running as administrator (recommended for clean installation)
  UserInfo::GetAccountType
  Pop $0
  StrCmp $0 "Admin" admin_ok
  StrCmp $0 "Power" admin_ok
  
  ; Not admin - show warning but allow to continue
  MessageBox MB_YESNO|MB_ICONQUESTION "For best results, it's recommended to run the installer as Administrator.$\n$\nDo you want to continue anyway?" IDYES admin_ok
  Abort
  
  admin_ok:
!macroend

; Custom function for handling installation conflicts
!macro customInstallMode
  ; Check if Easy Voice is already installed
  ReadRegStr $0 HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\{${APP_GUID}}" "DisplayName"
  StrCmp $0 "" not_installed
  
  ; Already installed - ask user what to do
  MessageBox MB_YESNOCANCEL|MB_ICONQUESTION "Easy Voice is already installed on this system.$\n$\nYes = Upgrade/Reinstall$\nNo = Repair installation$\nCancel = Exit installer" IDYES upgrade_install IDNO repair_install IDCANCEL cancel_install_conflict
  
  cancel_install_conflict:
    Abort
  
  repair_install:
    DetailPrint "Performing repair installation..."
    Goto installation_continue
  
  upgrade_install:
    DetailPrint "Performing upgrade/reinstall..."
    Goto installation_continue
  
  not_installed:
    DetailPrint "Performing fresh installation..."
    Goto installation_continue
  
  installation_continue:
!macroend
