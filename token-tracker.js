// token-tracker.js
const fs = require('fs');
const path = require('path');
const { app } = require('electron');

class TokenTracker {
    constructor() {
        this.tokenDir = path.join(app.getPath('userData'), 'tokens');
        this.tokenFile = path.join(this.tokenDir, 'usage.json');
        this.currentUser = null;
        
        // Ensure token directory exists
        if (!fs.existsSync(this.tokenDir)) {
            fs.mkdirSync(this.tokenDir, { recursive: true });
        }
        
        this.loadTokenUsage();
    }

    setCurrentUser(user) {
        this.currentUser = user;
    }

    loadTokenUsage() {
        try {
            if (fs.existsSync(this.tokenFile)) {
                const data = fs.readFileSync(this.tokenFile, 'utf8');
                this.tokenData = JSON.parse(data);
            } else {
                this.tokenData = {
                    totalTokens: 0,
                    sessions: [],
                    dailyUsage: {}
                };
            }
        } catch (error) {
            console.error('❌ Failed to load token usage:', error);
            this.tokenData = {
                totalTokens: 0,
                sessions: [],
                dailyUsage: {}
            };
        }
    }

    saveTokenUsage() {
        try {
            fs.writeFileSync(this.tokenFile, JSON.stringify(this.tokenData, null, 2));
        } catch (error) {
            console.error('❌ Failed to save token usage:', error);
        }
    }

    estimateTokensFromText(text) {
        // Rough estimation: 1 token ≈ 4 characters for English text
        // Whisper typically uses fewer tokens than text generation models
        const chars = text.length;
        const estimatedTokens = Math.ceil(chars / 4);
        return Math.max(1, estimatedTokens); // Minimum 1 token
    }

    estimateTokensFromAudioDuration(durationSeconds) {
        // Rough estimation: Whisper uses approximately 50 tokens per minute of audio
        const minutes = durationSeconds / 60;
        const estimatedTokens = Math.ceil(minutes * 50);
        return Math.max(10, estimatedTokens); // Minimum 10 tokens for any audio
    }

    async trackTokenUsage(tokensUsed, context = {}) {
        try {
            const today = new Date().toISOString().split('T')[0];
            const timestamp = new Date().toISOString();

            // Update total tokens
            this.tokenData.totalTokens += tokensUsed;

            // Update daily usage
            if (!this.tokenData.dailyUsage[today]) {
                this.tokenData.dailyUsage[today] = 0;
            }
            this.tokenData.dailyUsage[today] += tokensUsed;

            // Add session record
            const sessionRecord = {
                timestamp,
                tokensUsed,
                service: 'whisper',
                user: this.currentUser ? {
                    id: this.currentUser.id,
                    username: this.currentUser.username
                } : null,
                context: context
            };

            this.tokenData.sessions.push(sessionRecord);

            // Keep only last 1000 sessions to prevent file from growing too large
            if (this.tokenData.sessions.length > 1000) {
                this.tokenData.sessions = this.tokenData.sessions.slice(-1000);
            }

            // Save to file (synchronous for speed)
            this.saveTokenUsage();

            console.log(`📊 Token usage tracked: ${tokensUsed} tokens (Total: ${this.tokenData.totalTokens})`);

            // Send to external API asynchronously (don't wait for it)
            if (this.currentUser && this.currentUser.token) {
                this.sendToExternalAPI(tokensUsed, sessionRecord).catch(error => {
                    console.warn('⚠️ External API call failed (non-blocking):', error.message);
                });
            }

            return true;
        } catch (error) {
            console.error('❌ Failed to track token usage:', error);
            return false;
        }
    }

    async sendToExternalAPI(tokensUsed, sessionRecord) {
        try {
            // Replace with your actual token tracking API endpoint
            const apiEndpoint = 'https://your-api-endpoint.com/api/track-tokens';

            const payload = {
                userId: this.currentUser.id,
                username: this.currentUser.username,
                tokensUsed: tokensUsed,
                timestamp: sessionRecord.timestamp,
                service: 'whisper',
                context: sessionRecord.context,
                appVersion: app.getVersion()
            };

            // Create AbortController for timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

            const response = await fetch(apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.currentUser.token}`
                },
                body: JSON.stringify(payload),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                console.log('✅ Token usage sent to external API');
            } else {
                console.warn('⚠️ Failed to send token usage to external API:', response.status);
            }
        } catch (error) {
            if (error.name === 'AbortError') {
                console.warn('⚠️ External API call timed out (5s)');
            } else {
                console.warn('⚠️ Error sending token usage to external API:', error.message);
            }
        }
    }

    getTotalTokens() {
        return this.tokenData.totalTokens;
    }

    getDailyUsage(date = null) {
        const targetDate = date || new Date().toISOString().split('T')[0];
        return this.tokenData.dailyUsage[targetDate] || 0;
    }

    getWeeklyUsage() {
        const today = new Date();
        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        
        let weeklyTotal = 0;
        for (let d = new Date(weekAgo); d <= today; d.setDate(d.getDate() + 1)) {
            const dateStr = d.toISOString().split('T')[0];
            weeklyTotal += this.tokenData.dailyUsage[dateStr] || 0;
        }
        
        return weeklyTotal;
    }

    getMonthlyUsage() {
        const today = new Date();
        const monthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
        
        let monthlyTotal = 0;
        for (let d = new Date(monthAgo); d <= today; d.setDate(d.getDate() + 1)) {
            const dateStr = d.toISOString().split('T')[0];
            monthlyTotal += this.tokenData.dailyUsage[dateStr] || 0;
        }
        
        return monthlyTotal;
    }

    getUsageStats() {
        return {
            total: this.getTotalTokens(),
            daily: this.getDailyUsage(),
            weekly: this.getWeeklyUsage(),
            monthly: this.getMonthlyUsage(),
            sessionsCount: this.tokenData.sessions.length
        };
    }

    clearUsageData() {
        this.tokenData = {
            totalTokens: 0,
            sessions: [],
            dailyUsage: {}
        };
        this.saveTokenUsage();
        console.log('✅ Token usage data cleared');
    }
}

module.exports = TokenTracker;
