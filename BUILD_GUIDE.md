# 🚀 Easy Voice - Cross-Platform Build Guide

This guide explains how to build Easy Voice for Windows, macOS, and Linux.

## 📋 Prerequisites

### Required Software
- **Node.js** (v16 or higher)
- **npm** (comes with Node.js)
- **Git** (for version control)

### Platform-Specific Requirements

#### Windows
- **Windows 10/11** (for native Windows builds)
- **Visual Studio Build Tools** (automatically installed with electron-builder)

#### macOS
- **macOS 10.15+** (for native Mac builds)
- **Xcode Command Line Tools**: `xcode-select --install`
- **Apple Developer Account** (for code signing - optional for development)

#### Linux
- **Ubuntu 18.04+** or equivalent
- **Build essentials**: `sudo apt-get install build-essential`

## 🛠️ Build Commands

### Quick Start
```bash
# Install dependencies
npm install

# Build for current platform
npm run build

# Build for specific platform
npm run build:win     # Windows
npm run build:mac     # macOS  
npm run build:linux   # Linux

# Build for all platforms (limited by current OS)
npm run build:all
```

### Advanced Build Options
```bash
# Clean build (removes dist folder)
npm run clean && npm run build

# Debug build with extra logging
npm run build:debug

# Development mode
npm run dev
```

## 🎯 Platform-Specific Instructions

### 🪟 Windows Builds

#### On Windows Machine:
```bash
npm run build:win
```
**Output**: `dist/Easy Voice Setup 1.0.0.exe`

#### Cross-Platform (from Mac/Linux):
```bash
npm run build:win
```
**Note**: Windows builds work from any platform.

### 🍎 macOS Builds

#### ⚠️ Important Limitation
**macOS builds can ONLY be created on macOS machines** due to Apple's restrictions.

#### On macOS Machine:
```bash
npm run build:mac
```
**Output**: `dist/Easy Voice-1.0.0.dmg`

#### From Windows/Linux:
❌ **Not possible directly**
✅ **Use GitHub Actions** (see below)

### 🐧 Linux Builds

#### On Any Platform:
```bash
npm run build:linux
```
**Output**: `dist/Easy Voice-1.0.0.AppImage`

## 🤖 CI/CD Solutions (Recommended for Mac Builds)

### GitHub Actions

#### Setup Instructions
1. **Push to GitHub**:
   ```bash
   git add .
   git commit -m "Setup cross-platform builds"
   git push origin main
   ```

2. **Create a Release Tag**:
   ```bash
   git tag v1.0.0
   git push origin v1.0.0
   ```

3. **Automatic Builds**:
   - GitHub Actions will automatically build for Windows and macOS
   - Downloads available in the "Actions" tab
   - Release files attached to the GitHub release

### Bitbucket Pipelines

#### Setup Instructions
1. **Push to Bitbucket**:
   ```bash
   git remote add origin https://bitbucket.org/yourusername/easy-voice.git
   git push -u origin main
   ```

2. **Enable Pipelines**:
   - Go to Repository settings → Pipelines → Settings
   - Enable Pipelines

3. **Create Release Tag**:
   ```bash
   git tag v1.0.0
   git push origin v1.0.0
   ```

4. **Manual Builds**:
   - Go to Pipelines → Run pipeline
   - Select custom: `build-all`, `build-windows`, `build-macos`, or `build-linux`

#### Cost Considerations
- **Free tier**: Linux builds only (50 minutes/month)
- **Paid plans**: Windows/macOS builds ($3-6/month)
- **Self-hosted runners**: Free alternative for Windows/macOS

## 📁 Output Files

### Windows
- `Easy Voice Setup 1.0.0.exe` - Installer
- `Easy Voice 1.0.0.exe` - Portable version

### macOS
- `Easy Voice-1.0.0.dmg` - Disk image installer
- `Easy Voice-1.0.0-mac.zip` - App bundle

### Linux
- `Easy Voice-1.0.0.AppImage` - Portable application

## 🔧 Troubleshooting

### Common Issues

#### "Cannot build for macOS on Windows"
**Solution**: Use GitHub Actions or build on a Mac machine.

#### "electron-builder not found"
```bash
npm install -g electron-builder
# or
npm install
```

#### "Permission denied" on Linux
```bash
chmod +x dist/Easy\ Voice-1.0.0.AppImage
```

#### Build fails with native dependencies
```bash
npm run clean
npm install
npm rebuild
npm run build
```

### Build Optimization

#### Reduce Bundle Size
```bash
# Remove development dependencies
npm prune --production

# Build with compression
npm run build
```

#### Speed Up Builds
```bash
# Use local cache
export ELECTRON_CACHE=/path/to/cache

# Parallel builds
npm run build:all
```

## 🎨 Customization

### App Icons
Place your icons in the `assets/` folder:
- `icon.ico` - Windows icon (256x256)
- `icon.icns` - macOS icon (512x512)
- `icon.png` - Linux icon (512x512)

### Build Configuration
Edit `package.json` under the `"build"` section:
```json
{
  "build": {
    "appId": "com.easyvoice.app",
    "productName": "Easy Voice",
    "directories": {
      "output": "dist"
    }
  }
}
```

## 🚀 Distribution

### Windows
- Upload `.exe` files to your website
- Consider code signing for Windows Defender

### macOS
- Upload `.dmg` files
- Consider notarization for Gatekeeper

### Linux
- Upload `.AppImage` files
- Consider creating `.deb` and `.rpm` packages

## 📝 Release Checklist

- [ ] Update version in `package.json`
- [ ] Test on target platforms
- [ ] Create GitHub release tag
- [ ] Wait for GitHub Actions to complete
- [ ] Download and test built applications
- [ ] Upload to distribution channels

## 🆘 Support

If you encounter issues:
1. Check the [Electron Builder documentation](https://www.electron.build/)
2. Review GitHub Actions logs
3. Check platform-specific requirements
4. Ensure all dependencies are installed

---

**Happy Building! 🎉**
