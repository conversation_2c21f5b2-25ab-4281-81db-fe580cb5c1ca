#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🎨 Easy Voice - Icon Preparation Script');
console.log('======================================');

// Check if assets directory exists
const assetsDir = path.join(__dirname, '..', 'assets');
if (!fs.existsSync(assetsDir)) {
    fs.mkdirSync(assetsDir, { recursive: true });
    console.log('📁 Created assets directory');
}

// Check if icon.png exists in root
const rootIcon = path.join(__dirname, '..', 'icon.png');
const assetsIcon = path.join(assetsDir, 'icon.png');

if (fs.existsSync(rootIcon)) {
    // Copy icon.png to assets folder
    fs.copyFileSync(rootIcon, assetsIcon);
    console.log('✅ Copied icon.png to assets/icon.png');
} else {
    console.log('⚠️  No icon.png found in root directory');
}

// Create a simple .gitkeep file for assets directory
const gitkeepPath = path.join(assetsDir, '.gitkeep');
if (!fs.existsSync(gitkeepPath)) {
    fs.writeFileSync(gitkeepPath, '# Keep this directory in git\n');
    console.log('📝 Created assets/.gitkeep');
}

console.log('');
console.log('📋 Icon Status:');
console.log(`   - PNG Icon: ${fs.existsSync(assetsIcon) ? '✅' : '❌'}`);
console.log(`   - ICO Icon: ❌ (Windows - requires conversion)`);
console.log(`   - ICNS Icon: ❌ (macOS - requires conversion)`);

console.log('');
console.log('💡 For professional builds, consider:');
console.log('   1. Converting PNG to ICO for Windows');
console.log('   2. Converting PNG to ICNS for macOS');
console.log('   3. Using tools like electron-icon-builder');
console.log('');
console.log('🎉 Icon preparation completed!');
