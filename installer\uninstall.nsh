; Custom uninstall script for Easy Voice
; This script ensures clean uninstallation by stopping processes and removing auto-start

; Custom function to run before installer starts (pre-installation check)
!macro customInit
  ; Check for running Easy Voice processes before showing installer UI
  DetailPrint "Checking for running Easy Voice processes..."

  ; Use simple process detection first
  FindWindow $0 "" "Easy Voice"
  StrCmp $0 0 check_other_processes

  ; Found Easy Voice window - ask user to close it
  MessageBox MB_YESNO|MB_ICONQUESTION "Easy Voice is currently running and must be closed before installation.$\n$\nClose Easy Voice and continue with installation?" IDYES close_processes IDNO cancel_init

  check_other_processes:
  ; Check for processes by executable name
  nsExec::ExecToStack 'tasklist /FI "IMAGENAME eq Easy Voice.exe" /FO CSV | find /C "Easy Voice.exe"'
  Pop $0 ; Exit code
  Pop $1 ; Output (count)

  ; If count > 0, processes are running
  StrCmp $1 "0" no_processes_init

  ; Processes found - ask user
  MessageBox MB_YESNO|MB_ICONQUESTION "Easy Voice processes are currently running and must be closed before installation.$\n$\nClose Easy Voice and continue with installation?" IDYES close_processes <PERSON>N<PERSON> cancel_init

  close_processes:
    DetailPrint "Closing Easy Voice processes..."
    ; Try graceful close first
    nsExec::ExecToLog 'taskkill /IM "Easy Voice.exe" /T'
    nsExec::ExecToLog 'taskkill /IM "EasyVoice.exe" /T'
    nsExec::ExecToLog 'taskkill /IM "voicea.exe" /T'
    Sleep 3000

    ; Force close if still running
    nsExec::ExecToLog 'taskkill /F /IM "Easy Voice.exe" /T'
    nsExec::ExecToLog 'taskkill /F /IM "EasyVoice.exe" /T'
    nsExec::ExecToLog 'taskkill /F /IM "voicea.exe" /T'
    Sleep 1000

    DetailPrint "Easy Voice processes closed. Continuing with installation..."
    Goto no_processes_init

  cancel_init:
    MessageBox MB_OK|MB_ICONINFORMATION "Installation cancelled. Please close Easy Voice and run the installer again."
    Abort

  no_processes_init:
    DetailPrint "Ready to proceed with installation."
!macroend

; Custom installer configuration for finish page
!macro customInstallMode
  ; This macro ensures the finish page shows the run option
  DetailPrint "Configuring installation finish options..."
!macroend

!macro customUnInstall
  ; Stop all Easy Voice processes before uninstalling
  DetailPrint "Stopping Easy Voice processes..."
  
  ; Kill all Easy Voice processes
  nsExec::ExecToLog 'taskkill /F /IM "Easy Voice.exe" /T'
  nsExec::ExecToLog 'taskkill /F /IM "EasyVoice.exe" /T'
  nsExec::ExecToLog 'taskkill /F /IM "voicea.exe" /T'
  
  ; Wait a moment for processes to terminate
  Sleep 2000
  
  ; Remove auto-start registry entries
  DetailPrint "Removing auto-start configuration..."
  
  ; Remove from current user startup
  DeleteRegValue HKCU "Software\Microsoft\Windows\CurrentVersion\Run" "Easy Voice"
  DeleteRegValue HKCU "Software\Microsoft\Windows\CurrentVersion\Run" "EasyVoice"
  DeleteRegValue HKCU "Software\Microsoft\Windows\CurrentVersion\Run" "VoiceFlow"
  
  ; Remove from all users startup (if exists)
  DeleteRegValue HKLM "Software\Microsoft\Windows\CurrentVersion\Run" "Easy Voice"
  DeleteRegValue HKLM "Software\Microsoft\Windows\CurrentVersion\Run" "EasyVoice"
  DeleteRegValue HKLM "Software\Microsoft\Windows\CurrentVersion\Run" "VoiceFlow"
  
  ; Clean up user data directory (optional - ask user)
  MessageBox MB_YESNO|MB_ICONQUESTION "Do you want to remove all user data and settings? This cannot be undone." IDNO skip_userdata
  
  ; Remove user data if user confirmed
  RMDir /r "$APPDATA\Easy Voice"
  RMDir /r "$APPDATA\EasyVoice"
  RMDir /r "$APPDATA\voicea"
  
  skip_userdata:
  
  ; Clean up any remaining shortcuts
  DetailPrint "Cleaning up shortcuts..."
  Delete "$DESKTOP\Easy Voice.lnk"
  Delete "$SMPROGRAMS\Easy Voice.lnk"
  Delete "$SMPROGRAMS\Easy Voice\Easy Voice.lnk"
  Delete "$SMPROGRAMS\Easy Voice\Uninstall Easy Voice.lnk"
  RMDir "$SMPROGRAMS\Easy Voice"
  
  ; Clean up temporary files
  DetailPrint "Cleaning up temporary files..."
  RMDir /r "$TEMP\Easy Voice"
  RMDir /r "$TEMP\EasyVoice"
  
  DetailPrint "Easy Voice has been completely removed from your system."
!macroend

; Custom function to run before uninstaller starts
!macro customUninstallCheck
  ; Run pre-uninstall PowerShell script for comprehensive cleanup
  DetailPrint "Running pre-uninstall cleanup..."

  ; Execute PowerShell script with execution policy bypass
  nsExec::ExecToLog 'powershell.exe -ExecutionPolicy Bypass -WindowStyle Hidden -File "$INSTDIR\installer\pre-uninstall.ps1"'

  ; Fallback: Check if Easy Voice is still running after script
  FindWindow $0 "" "Easy Voice"
  StrCmp $0 0 notRunning

  ; App is still running, force close it
  DetailPrint "Force closing remaining processes..."
  nsExec::ExecToLog 'taskkill /F /IM "Easy Voice.exe" /T'
  nsExec::ExecToLog 'taskkill /F /IM "EasyVoice.exe" /T'
  nsExec::ExecToLog 'taskkill /F /IM "voicea.exe" /T'
  Sleep 2000

  notRunning:
!macroend
