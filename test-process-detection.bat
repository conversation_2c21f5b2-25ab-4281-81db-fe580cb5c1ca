@echo off
echo Easy Voice - Process Detection Test
echo ==================================
echo.
echo This script tests the process detection methods used by the installer.
echo.

echo Testing Method 1: tasklist command
echo ----------------------------------
tasklist /FI "IMAGENAME eq Easy Voice.exe" /FO CSV | find /C "Easy Voice.exe"
set method1_result=%ERRORLEVEL%
echo Method 1 result: %method1_result%

echo.
echo Testing Method 2: PowerShell Get-Process
echo ----------------------------------------
powershell.exe -Command "Get-Process -Name 'Easy Voice' -ErrorAction SilentlyContinue | Measure-Object | Select-Object -ExpandProperty Count"
set method2_result=%ERRORLEVEL%
echo Method 2 result: %method2_result%

echo.
echo Testing Method 3: Direct process check
echo --------------------------------------
tasklist | findstr /i "Easy Voice"
set method3_result=%ERRORLEVEL%
echo Method 3 result: %method3_result%

echo.
echo Results Summary:
echo ===============
if %method1_result%==0 (
    echo ✅ Method 1: Process detection working
) else (
    echo ❌ Method 1: No processes found or error
)

if %method2_result%==0 (
    echo ✅ Method 2: PowerShell detection working  
) else (
    echo ❌ Method 2: No processes found or error
)

if %method3_result%==0 (
    echo ✅ Method 3: Direct search found processes
) else (
    echo ❌ Method 3: No processes found
)

echo.
echo Note: If Easy Voice is not running, all methods should show "No processes found"
echo If Easy Voice IS running, at least one method should detect it.
echo.
pause
