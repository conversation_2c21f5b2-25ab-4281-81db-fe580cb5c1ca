# 🚀 Easy Voice - Bitbucket Pipelines Build Guide

This guide explains how to set up cross-platform builds using Bitbucket Pipelines for Windows, macOS, and Linux.

## 📋 Prerequisites

### Bitbucket Account Setup
- **Bitbucket account** with Pipelines enabled
- **Repository** pushed to Bitbucket
- **Pipelines minutes** (free tier includes 50 minutes/month)

### Required Runners
- **Linux**: Default (included in free tier)
- **Windows**: Requires paid plan or self-hosted runner
- **macOS**: Requires paid plan or self-hosted runner

## 🛠️ Setup Instructions

### 1. Enable Bitbucket Pipelines

1. Go to your repository in Bitbucket
2. Navigate to **Repository settings** → **Pipelines** → **Settings**
3. Enable **Pipelines**
4. Confirm the `bitbucket-pipelines.yml` file is in your repository root

### 2. Configure Repository Variables (Optional)

For code signing, add these in **Repository settings** → **Pipelines** → **Repository variables**:

#### Windows Code Signing
- `WINDOWS_CSC_LINK` - Base64 encoded certificate
- `WINDOWS_CSC_PASSWORD` - Certificate password

#### macOS Code Signing
- `MACOS_CSC_LINK` - Base64 encoded certificate
- `MACOS_CSC_PASSWORD` - Certificate password
- `APPLE_ID` - Apple ID email
- `APPLE_ID_PASSWORD` - App-specific password

### 3. Push Your Code

```bash
# Add Bitbucket as remote (if not already)
git remote add origin https://bitbucket.org/yourusername/easy-voice.git

# Push your code
git add .
git commit -m "Setup Bitbucket Pipelines"
git push -u origin main
```

## 🚀 Build Methods

### Method 1: Automatic Builds (Tags)

Create a release tag to trigger automatic builds:

```bash
# Create and push a tag
git tag v1.0.0
git push origin v1.0.0
```

**Result**: Builds for Windows, macOS, and Linux automatically start in parallel.

### Method 2: Manual Builds

1. Go to **Pipelines** in your Bitbucket repository
2. Click **Run pipeline**
3. Select branch: `main`
4. Choose custom pipeline:
   - `build-all` - Build for all platforms
   - `build-windows` - Windows only
   - `build-macos` - macOS only
   - `build-linux` - Linux only

### Method 3: Development Builds

Push to `main` branch for automatic test builds:

```bash
git push origin main
```

**Result**: Creates a test build for the current platform.

## 📁 Download Built Apps

### From Pipelines

1. Go to **Pipelines** → Select your build
2. Click on the completed step
3. Scroll down to **Artifacts**
4. Download the built applications

### From Downloads Section

1. Go to **Downloads** in your repository
2. Find your build artifacts
3. Download the files you need

## 🎯 Platform-Specific Information

### 🪟 Windows Builds

#### Requirements
- **Windows runner** (paid feature or self-hosted)
- **Build time**: ~5-10 minutes

#### Output Files
- `Easy Voice Setup 1.0.0.exe` - NSIS installer
- `Easy Voice 1.0.0.exe` - Portable version

#### Runner Configuration
```yaml
runs-on:
  - 'windows'
```

### 🍎 macOS Builds

#### Requirements
- **macOS runner** (paid feature or self-hosted)
- **Build time**: ~10-15 minutes

#### Output Files
- `Easy Voice-1.0.0.dmg` - Disk image installer
- `Easy Voice-1.0.0-mac.zip` - App bundle

#### Runner Configuration
```yaml
runs-on:
  - 'macos'
```

### 🐧 Linux Builds

#### Requirements
- **Linux runner** (included in free tier)
- **Build time**: ~3-5 minutes

#### Output Files
- `Easy Voice-1.0.0.AppImage` - Portable application

#### Runner Configuration
```yaml
# Uses default Linux runner
image: node:18
```

## 💰 Cost Considerations

### Free Tier (50 minutes/month)
- **Linux builds**: ✅ Included
- **Windows builds**: ❌ Requires paid plan
- **macOS builds**: ❌ Requires paid plan

### Paid Plans
- **Standard ($3/month)**: 2,500 minutes
- **Premium ($6/month)**: 3,500 minutes
- **Windows/macOS**: ~10-15 minutes per build

### Self-Hosted Runners (Free Alternative)
Set up your own runners for Windows/macOS builds:

1. **Windows Runner**: Use your Windows machine
2. **macOS Runner**: Use a Mac machine or cloud service
3. **Configuration**: Follow Bitbucket's self-hosted runner guide

## 🔧 Troubleshooting

### Common Issues

#### "No runners available for Windows/macOS"
**Solution**: Upgrade to a paid plan or set up self-hosted runners.

#### "Pipeline failed on dependencies"
```bash
# Clear cache and retry
git commit --allow-empty -m "Clear pipeline cache"
git push origin main
```

#### "Build artifacts not found"
Check the pipeline logs for build errors:
1. Go to **Pipelines** → Select failed build
2. Click on the failed step
3. Review the logs for error messages

#### "Permission denied on Linux"
The built AppImage needs execute permissions:
```bash
chmod +x "Easy Voice-1.0.0.AppImage"
```

### Performance Optimization

#### Reduce Build Time
```yaml
# Use caches
definitions:
  caches:
    nodemodules: node_modules
```

#### Parallel Builds
```yaml
# Build platforms in parallel
- parallel:
    - step: *build-windows
    - step: *build-macos
    - step: *build-linux
```

## 🎨 Customization

### Custom Pipeline Triggers

#### Build on Pull Requests
```yaml
pull-requests:
  '**':
    - step:
        name: Test Build
        script:
          - npm ci
          - npm run build:current
```

#### Scheduled Builds
```yaml
# Build every night at 2 AM UTC
schedules:
  nightly-build:
    - cron: '0 2 * * *'
    - step:
        name: Nightly Build
        script:
          - npm run build:all
```

### Environment Variables

Set in **Repository settings** → **Pipelines** → **Repository variables**:

- `NODE_ENV=production`
- `ELECTRON_CACHE=/opt/atlassian/pipelines/agent/build/cache`
- `DEBUG_LOGS=false`

## 📊 Build Status

### Pipeline Status Badge

Add to your README.md:
```markdown
[![Build Status](https://img.shields.io/bitbucket/pipelines/yourusername/easy-voice)](https://bitbucket.org/yourusername/easy-voice/addon/pipelines/home)
```

### Build Notifications

Configure in **Repository settings** → **Pipelines** → **Notifications**:
- Email notifications
- Slack integration
- Custom webhooks

## 🚀 Deployment

### Automatic Deployment

Add deployment steps to your pipeline:

```yaml
- step:
    name: Deploy to Website
    deployment: production
    script:
      - # Upload to your website
      - # Update download links
      - # Notify users
```

### Integration with App Stores

- **Microsoft Store**: Use Windows App Certification Kit
- **Mac App Store**: Configure for App Store distribution
- **Snap Store**: Add snap packaging for Linux

## 📝 Release Workflow

### Complete Release Process

1. **Update Version**:
   ```bash
   npm version patch  # or minor/major
   ```

2. **Create Release Tag**:
   ```bash
   git tag v$(node -p "require('./package.json').version")
   git push origin --tags
   ```

3. **Monitor Builds**:
   - Check Pipelines for build status
   - Download and test artifacts

4. **Distribute**:
   - Upload to your website
   - Update documentation
   - Announce release

---

## 🆘 Support Resources

- [Bitbucket Pipelines Documentation](https://support.atlassian.com/bitbucket-cloud/docs/get-started-with-bitbucket-pipelines/)
- [Electron Builder Documentation](https://www.electron.build/)
- [Self-Hosted Runners Guide](https://support.atlassian.com/bitbucket-cloud/docs/runners/)

**Happy Building with Bitbucket! 🎉**
