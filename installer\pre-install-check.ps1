# Pre-installation check script for Easy Voice
# This script detects running Easy Voice processes and handles them gracefully

param(
    [switch]$Silent = $false,
    [switch]$ForceClose = $false
)

Write-Host "Easy Voice - Pre-Installation Check" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan

# Function to check if Easy Voice processes are running
function Test-EasyVoiceRunning {
    $processNames = @("Easy Voice", "EasyVoice", "voicea")
    $runningProcesses = @()
    
    foreach ($processName in $processNames) {
        $processes = Get-Process -Name $processName -ErrorAction SilentlyContinue
        if ($processes) {
            $runningProcesses += $processes
        }
    }
    
    return $runningProcesses
}

# Function to gracefully close Easy Voice
function Stop-EasyVoiceGracefully {
    param($processes)
    
    Write-Host "Attempting graceful shutdown..." -ForegroundColor Yellow
    
    foreach ($process in $processes) {
        try {
            # Try to close main window first
            $process.CloseMainWindow()
            Write-Host "Sent close signal to: $($process.ProcessName) (PID: $($process.Id))" -ForegroundColor Green
        } catch {
            Write-Host "Could not send close signal to: $($process.ProcessName)" -ForegroundColor Yellow
        }
    }
    
    # Wait for processes to close gracefully
    Write-Host "Waiting for processes to close..." -ForegroundColor Yellow
    Start-Sleep -Seconds 5
    
    # Check if processes are still running
    $stillRunning = Test-EasyVoiceRunning
    return $stillRunning
}

# Function to force close Easy Voice processes
function Stop-EasyVoiceForce {
    param($processes)
    
    Write-Host "Force closing remaining processes..." -ForegroundColor Red
    
    foreach ($process in $processes) {
        try {
            $process.Kill()
            Write-Host "Force stopped: $($process.ProcessName) (PID: $($process.Id))" -ForegroundColor Red
        } catch {
            Write-Host "Failed to force stop: $($process.ProcessName)" -ForegroundColor Red
        }
    }
    
    Start-Sleep -Seconds 2
}

# Main execution
try {
    $runningProcesses = Test-EasyVoiceRunning
    
    if ($runningProcesses.Count -eq 0) {
        Write-Host "No Easy Voice processes detected. Installation can proceed." -ForegroundColor Green
        exit 0
    }
    
    Write-Host "Found $($runningProcesses.Count) Easy Voice process(es) running:" -ForegroundColor Yellow
    foreach ($process in $runningProcesses) {
        Write-Host "  - $($process.ProcessName) (PID: $($process.Id))" -ForegroundColor Yellow
    }
    
    if ($Silent -and -not $ForceClose) {
        Write-Host "Silent mode: Cannot proceed with installation while processes are running." -ForegroundColor Red
        exit 1
    }
    
    if ($ForceClose) {
        Write-Host "Force close mode: Terminating all Easy Voice processes..." -ForegroundColor Red
        Stop-EasyVoiceForce $runningProcesses
    } else {
        # Interactive mode - ask user
        Write-Host ""
        Write-Host "Easy Voice must be closed before installation can continue." -ForegroundColor Yellow
        $response = Read-Host "Close Easy Voice and continue installation? (Y/N)"
        
        if ($response -eq 'Y' -or $response -eq 'y') {
            # Try graceful shutdown first
            $stillRunning = Stop-EasyVoiceGracefully $runningProcesses
            
            if ($stillRunning.Count -gt 0) {
                Write-Host ""
                Write-Host "Some processes are still running. Force close them?" -ForegroundColor Yellow
                $forceResponse = Read-Host "Force close remaining processes? (Y/N)"
                
                if ($forceResponse -eq 'Y' -or $forceResponse -eq 'y') {
                    Stop-EasyVoiceForce $stillRunning
                } else {
                    Write-Host "Installation cancelled by user." -ForegroundColor Red
                    exit 1
                }
            }
        } else {
            Write-Host "Installation cancelled by user." -ForegroundColor Red
            exit 1
        }
    }
    
    # Final check
    Start-Sleep -Seconds 1
    $finalCheck = Test-EasyVoiceRunning
    
    if ($finalCheck.Count -eq 0) {
        Write-Host ""
        Write-Host "All Easy Voice processes have been stopped. Installation can proceed." -ForegroundColor Green
        exit 0
    } else {
        Write-Host ""
        Write-Host "Some processes are still running. Installation cannot proceed safely." -ForegroundColor Red
        Write-Host "Please manually close Easy Voice and try again." -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Host ""
    Write-Host "An error occurred during pre-installation check: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Installation may not proceed safely." -ForegroundColor Red
    exit 1
}
