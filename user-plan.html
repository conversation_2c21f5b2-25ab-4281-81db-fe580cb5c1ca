<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Plan Details - VoiceA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: white;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            animation: fadeInUp 0.8s ease;
        }

        @keyframes fadeInUp {
            0% {
                opacity: 0;
                transform: translateY(30px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .user-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .user-info h2 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .user-detail {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .user-detail:last-child {
            border-bottom: none;
        }

        .user-detail .label {
            font-weight: 600;
            opacity: 0.8;
        }

        .user-detail .value {
            color: #4ecdc4;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.15);
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #4ecdc4;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .plan-details {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .plan-details h2 {
            color: #4ecdc4;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .plan-feature {
            display: flex;
            align-items: center;
            margin-bottom: 0.8rem;
            padding: 0.5rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .plan-feature .icon {
            margin-right: 1rem;
            font-size: 1.2rem;
        }

        .buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        button {
            padding: 12px 24px;
            border: none;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            font-weight: 500;
        }

        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        button.primary {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            border: none;
        }

        button.primary:hover {
            background: linear-gradient(135deg, #5fd3d0, #4fb3a6);
        }

        .loading {
            text-align: center;
            padding: 2rem;
            opacity: 0.7;
        }

        .error {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
            border: 1px solid rgba(255, 107, 107, 0.3);
            border-radius: 15px;
            padding: 1rem;
            text-align: center;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 User Plan Details</h1>
        </div>

        <div id="loadingDiv" class="loading">
            <p>Loading user information...</p>
        </div>

        <div id="errorDiv" class="error" style="display: none;">
            <p id="errorMessage">Failed to load user information</p>
        </div>

        <div id="contentDiv" style="display: none;">
            <div class="user-info">
                <h2>👤 User Information</h2>
                <div class="user-detail">
                    <span class="label">Username:</span>
                    <span class="value" id="username">-</span>
                </div>
                <div class="user-detail">
                    <span class="label">Full Name:</span>
                    <span class="value" id="fullName">-</span>
                </div>
                <div class="user-detail">
                    <span class="label">Email:</span>
                    <span class="value" id="email">-</span>
                </div>
                <div class="user-detail">
                    <span class="label">User ID:</span>
                    <span class="value" id="userId">-</span>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalTokens">0</div>
                    <div class="stat-label">Total Tokens Used</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="dailyTokens">0</div>
                    <div class="stat-label">Today's Usage</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="weeklyTokens">0</div>
                    <div class="stat-label">This Week</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="monthlyTokens">0</div>
                    <div class="stat-label">This Month</div>
                </div>
            </div>

            <div class="plan-details">
                <h2>🎯 Plan Features</h2>
                <div class="plan-feature">
                    <span class="icon">🎤</span>
                    <span>Unlimited voice recordings</span>
                </div>
                <div class="plan-feature">
                    <span class="icon">🧠</span>
                    <span>AI-powered transcription with Whisper</span>
                </div>
                <div class="plan-feature">
                    <span class="icon">🌐</span>
                    <span>Multi-language support</span>
                </div>
                <div class="plan-feature">
                    <span class="icon">⚡</span>
                    <span>Real-time processing</span>
                </div>
                <div class="plan-feature">
                    <span class="icon">🔒</span>
                    <span>Secure and private</span>
                </div>
            </div>
        </div>

        <div class="buttons">
            <button class="primary" onclick="refreshData()">Refresh Data</button>
            <button onclick="closeWindow()">Close</button>
        </div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');

        let currentUser = null;

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', async () => {
            await loadUserData();
        });

        async function loadUserData() {
            try {
                showLoading();

                // Get saved auth data
                const authData = await ipcRenderer.invoke('get-saved-auth');
                if (!authData || !authData.user) {
                    throw new Error('No user data found');
                }

                currentUser = authData.user;

                // Get token statistics
                const tokenStats = await ipcRenderer.invoke('get-token-stats');

                // Update UI
                updateUserInfo(currentUser);
                updateTokenStats(tokenStats);

                showContent();
            } catch (error) {
                console.error('Error loading user data:', error);
                showError(error.message);
            }
        }

        function updateUserInfo(user) {
            document.getElementById('username').textContent = user.username || '-';
            document.getElementById('fullName').textContent = 
                user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : '-';
            document.getElementById('email').textContent = user.email || '-';
            document.getElementById('userId').textContent = user.id || '-';
        }

        function updateTokenStats(stats) {
            document.getElementById('totalTokens').textContent = stats.total || 0;
            document.getElementById('dailyTokens').textContent = stats.daily || 0;
            document.getElementById('weeklyTokens').textContent = stats.weekly || 0;
            document.getElementById('monthlyTokens').textContent = stats.monthly || 0;
        }

        function showLoading() {
            document.getElementById('loadingDiv').style.display = 'block';
            document.getElementById('errorDiv').style.display = 'none';
            document.getElementById('contentDiv').style.display = 'none';
        }

        function showError(message) {
            document.getElementById('loadingDiv').style.display = 'none';
            document.getElementById('errorDiv').style.display = 'block';
            document.getElementById('contentDiv').style.display = 'none';
            document.getElementById('errorMessage').textContent = message;
        }

        function showContent() {
            document.getElementById('loadingDiv').style.display = 'none';
            document.getElementById('errorDiv').style.display = 'none';
            document.getElementById('contentDiv').style.display = 'block';
        }

        async function refreshData() {
            await loadUserData();
        }

        function closeWindow() {
            window.close();
        }
    </script>
</body>
</html>
