


// main.js
const { app, BrowserWindow, ipcMain, clipboard, screen, Tray, Menu } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');
require('dotenv').config();
const fetch = require('node-fetch');

// Simple and reliable path resolution - works in both dev and production
function getAppPath(relativePath) {
  return path.join(__dirname, relativePath);
}

// Import our custom services
const AuthService = require('./auth-service');
const TokenTracker = require('./token-tracker');
// Import OpenAI with error handling



const AutoLaunch = require('auto-launch');

const appAutoLauncher = new AutoLaunch({
  name: 'VoiceFlow',
  path: process.execPath,
});

appAutoLauncher.isEnabled().then((isEnabled) => {
  if (!isEnabled) appAutoLauncher.enable();
}).catch(console.error);

// Function to clean up auto-launch and processes during uninstall
async function cleanupForUninstall() {
  console.log('🧹 Performing cleanup for uninstall...');

  try {
    // Disable auto-launch
    const isEnabled = await appAutoLauncher.isEnabled();
    if (isEnabled) {
      await appAutoLauncher.disable();
      console.log('✅ Auto-launch disabled');
    }
  } catch (error) {
    console.warn('⚠️ Error disabling auto-launch:', error);
  }

  // Stop recording if active
  if (isRecording) {
    stopRecording();
  }

  // Stop uiohook
  try {
    uIOhook.stop();
    console.log('✅ uIOhook stopped');
  } catch (error) {
    console.warn('⚠️ Error stopping uIOhook:', error);
  }

  // Clean up timeouts
  if (recordingTimeout) {
    clearTimeout(recordingTimeout);
  }

  console.log('✅ Cleanup completed');
}



// Capture original methods FIRST
const originalConsole = {
  log: console.log,
  warn: console.warn,
  error: console.error
};

const logDir = path.join(app.getPath('userData'), 'logs');
const logFile = path.join(logDir, 'voicea.log');

fs.mkdirSync(logDir, { recursive: true });
const logStream = fs.createWriteStream(logFile, { flags: 'a' });

function logToFile(level, ...args) {
  const time = new Date().toISOString();
  const message = `[${time}] [${level.toUpperCase()}] ${args.join(' ')}\n`;
  try {
    logStream.write(message);
  } catch (_) {
    // Fail silently if write fails
  }
  originalConsole[level](...args); // ✅ Use original to avoid recursion
}

// Override
console.log = (...args) => logToFile('log', ...args);
console.warn = (...args) => logToFile('warn', ...args);
console.error = (...args) => logToFile('error', ...args);


let openai = null;
let authService = null;
let tokenTracker = null;
let currentUser = null;

let OpenAI;
try {
  OpenAI = require('openai');
  console.log('✅ OpenAI package loaded successfully');
} catch (error) {
  console.error('❌ OpenAI package not found. Please install it with: npm install openai');
  console.error('Error details:', error.message);
}

// Import uiohook-napi correctly
const { uIOhook, UiohookKey } = require('uiohook-napi');

let recordingWindow;
let userPlanWindow;
let mainWindow;

let tray;

// Recording state variables
let ctrlDown = false;
let altDown = false;
let shiftDown = false;
let keyDown = null;
let isRecording = false;
let recordingTimeout;
let isMainWindowVisible = true;
let shortcutsEnabled = false;

// Current shortcut configuration
let currentShortcut = { ctrl: true, alt: true, shift: false, key: null };

// Performance tracking
let performanceStats = {
  totalTranscriptions: 0,
  totalTranscriptionTime: 0,
  averageTranscriptionTime: 0
};

function updatePerformanceStats(transcriptionTime) {
  performanceStats.totalTranscriptions++;
  performanceStats.totalTranscriptionTime += transcriptionTime;
  performanceStats.averageTranscriptionTime = performanceStats.totalTranscriptionTime / performanceStats.totalTranscriptions;

  console.log(`📈 Performance: Avg transcription time: ${performanceStats.averageTranscriptionTime.toFixed(2)}s (${performanceStats.totalTranscriptions} total)`);
}

async function initializeServices() {
  // Initialize authentication service
  authService = new AuthService();

  // Initialize token tracker
  tokenTracker = new TokenTracker();

  // Try auto-login first
  try {
    const autoLoginResult = await authService.autoLogin();
    if (autoLoginResult) {
      currentUser = autoLoginResult.user;
      tokenTracker.setCurrentUser(currentUser);

      // Check if user is active before enabling shortcuts
      if (currentUser.isActive) {
        shortcutsEnabled = true;
        console.log('✅ Auto-login successful for user:', currentUser.username);
        console.log('✅ Shortcuts enabled for active user');
      } else {
        shortcutsEnabled = false;
        console.log('⚠️ Auto-login successful but user subscription expired:', currentUser.username);
        console.log('❌ Shortcuts disabled for inactive user');
      }
    } else {
      shortcutsEnabled = false;
      console.log('⚠️ No auto-login available, shortcuts disabled');
    }
  } catch (error) {
    console.error('❌ Auto-login error:', error);
    shortcutsEnabled = false;

    // Clear stored credentials when auto-login fails
    if (authService) {
      await authService.clearAuth();
      console.log('🗑️ Cleared stored credentials due to auto-login failure');
    }

    // Show user-friendly error message when main window is ready
    setTimeout(() => {
      if (mainWindow) {
        mainWindow.webContents.executeJavaScript(`
          // First, update the loading status to show error
          const loadingStatus = document.getElementById('loadingStatus');
          if (loadingStatus) {
            loadingStatus.textContent = 'Auto-login failed. Please login manually.';
            loadingStatus.style.color = '#ff6b6b';
          }

          // Then after a delay, show the login form with error message
          setTimeout(() => {
            // Hide loading container
            const loadingContainer = document.getElementById('loadingContainer');
            if (loadingContainer) {
              loadingContainer.style.display = 'none';
            }

            // Show login container
            const loginContainer = document.getElementById('loginContainer');
            if (loginContainer) {
              loginContainer.style.display = 'block';

              // Add error message to login form
              const statusDiv = document.querySelector('.login-status') || document.createElement('div');
              statusDiv.className = 'login-status error';
              statusDiv.textContent = 'Auto-login failed. Please enter your credentials to continue.';
              statusDiv.style.cssText = 'color: #ff6b6b; text-align: center; margin: 10px 0; padding: 10px; background: rgba(255, 107, 107, 0.1); border-radius: 6px; border: 1px solid rgba(255, 107, 107, 0.3);';

              if (!document.querySelector('.login-status')) {
                const loginForm = loginContainer.querySelector('.login-form-container');
                if (loginForm) {
                  loginForm.appendChild(statusDiv);
                }
              }
            }
          }, 2000); // Wait 2 seconds to show error, then switch to login
        `).catch(e => console.log('Could not show error message:', e));
      }
    }, 1000); // Wait 1 second for window to be ready
  }

  await initializeOpenAI();
}

async function initializeOpenAI() {
  try {
    const response = await fetch('https://easyvoice.kambaaincorporation.in/apiv2/get-openai-key', {
      headers: {
        Authorization: 'Bearer NodeApiAuth@2025'
      }
    });
    const data = await response.json();
    const apiKey = data?.OPENAI_API_KEY;

    if (!apiKey) throw new Error('API key missing in response');

    openai = new OpenAI({
      apiKey: apiKey
    });
    console.log('✅ OpenAI initialized');
  } catch (err) {
    console.error('❌ Failed to initialize OpenAI:', err.message);
  }
}



function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 1000,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      zoomFactor: 1.0, // Set default zoom
      enableRemoteModule: false
    },
    icon: getAppPath(path.join('assets', 'icon.png')), // Simple path resolution
    show: false, // Don't show until ready
    autoHideMenuBar: true, // Hide menu bar (File, Edit, View, etc.)
    menuBarVisible: false // Ensure menu bar is not visible
  });

  mainWindow.loadFile(getAppPath('main.html')); // Use proper path resolution

  // Remove menu bar completely
  mainWindow.setMenuBarVisibility(false);
  mainWindow.setAutoHideMenuBar(true);

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    // Disable zoom functionality
    mainWindow.webContents.setZoomFactor(1.0);
    mainWindow.webContents.on('zoom-changed', (event, zoomDirection) => {
      event.preventDefault();
      mainWindow.webContents.setZoomFactor(1.0);
    });

    // Disable zoom keyboard shortcuts
    mainWindow.webContents.on('before-input-event', (event, input) => {
      if (input.control && (input.key === '=' || input.key === '-' || input.key === '0')) {
        event.preventDefault();
      }
    });

    mainWindow.show();
    console.log('✅ Main window ready');
  });

  // Handle window closed
  mainWindow.on('close', (event) => {
    if (isMainWindowVisible) {
      event.preventDefault();
      mainWindow.hide();
      isMainWindowVisible = false;
      console.log('📱 Main window minimized to tray');
    }
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}



function createRecordingWindow() {
  recordingWindow = new BrowserWindow({
    width: 300,
    height: 100,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    resizable: false,
    skipTaskbar: true,
    show: false, // Don't show initially
    focusable: false, // Prevent stealing focus from input fields
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
    }
  });

  recordingWindow.loadFile(getAppPath('recording-widget.html'));

  const { width } = screen.getPrimaryDisplay().workAreaSize;
  recordingWindow.setPosition(width - 320, 20);

  // Handle window closed
  recordingWindow.on('closed', () => {
    recordingWindow = null;
  });
}

function createUserPlanWindow() {
  if (userPlanWindow) {
    userPlanWindow.focus();
    return;
  }

  userPlanWindow = new BrowserWindow({
    width: 900,
    height: 700,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
    },
    icon: getAppPath(path.join('assets', 'icon.png')),
    show: false,
    parent: mainWindow,
    modal: false
  });

  userPlanWindow.loadFile(getAppPath('user-plan.html'));

  userPlanWindow.once('ready-to-show', () => {
    userPlanWindow.show();
  });

  userPlanWindow.on('closed', () => {
    userPlanWindow = null;
  });
}

function startRecording() {
  // Check if user is logged in before allowing recording
  if (!currentUser || !currentUser.userId) {
    console.log('❌ Recording blocked: User not logged in');
    if (mainWindow) {
      mainWindow.webContents.send('show-login-required');
    }
    return;
  }

  isRecording = true;
  console.log('🎙️ Ctrl + Alt held — START RECORDING (User:', currentUser.username, ')');

  // Show recording window without stealing focus
  if (!recordingWindow) {
    createRecordingWindow();
  }
  recordingWindow.showInactive(); // Show without stealing focus
  recordingWindow.webContents.send('start-recording');

  // Safety timeout
  recordingTimeout = setTimeout(() => {
    if (isRecording) {
      console.log('🛑 Auto-stop after 30 seconds');
      stopRecording();
    }
  }, 30000);
}

function stopRecording() {
  if (!isRecording) return;
  
  isRecording = false;
  console.log('🛑 Key released — STOP RECORDING');
  
  // Clear timeout
  if (recordingTimeout) {
    clearTimeout(recordingTimeout);
    recordingTimeout = null;
  }

  // Notify renderer to stop recording
  if (recordingWindow) {
    recordingWindow.webContents.send('stop-recording');
  }
}

function registerGlobalShortcuts() {
  try {
    // Start uiohook
    uIOhook.start();
    console.log('✅ Global shortcuts registered');

    // Keydown event handler
    uIOhook.on('keydown', (event) => {
      try {
        // Track modifier keys
        if (event.keycode === UiohookKey.Ctrl) {
          ctrlDown = true;
        }
        if (event.keycode === UiohookKey.Alt) {
          altDown = true;
        }
        if (event.keycode === UiohookKey.Shift) {
          shiftDown = true;
        }

        // Track regular keys if shortcut uses one
        if (currentShortcut.key) {
          const keyChar = getKeyFromCode(event.keycode);
          if (keyChar && keyChar.toLowerCase() === currentShortcut.key.toLowerCase()) {
            keyDown = keyChar;
          }
        }

        // Check if current combination matches the configured shortcut
        if (isShortcutPressed() && !isRecording && shortcutsEnabled) {
          startRecording();
        } else if (isShortcutPressed() && !shortcutsEnabled) {
          console.log('❌ Shortcuts disabled: User not logged in');
        }
      } catch (error) {
        console.error('❌ Error in keydown handler:', error);
      }
    });

    // Keyup event handler
    uIOhook.on('keyup', (event) => {
      try {
        // Track modifier keys
        if (event.keycode === UiohookKey.Ctrl) {
          ctrlDown = false;
        }
        if (event.keycode === UiohookKey.Alt) {
          altDown = false;
        }
        if (event.keycode === UiohookKey.Shift) {
          shiftDown = false;
        }

        // Track regular keys
        if (currentShortcut.key) {
          const keyChar = getKeyFromCode(event.keycode);
          if (keyChar && keyChar.toLowerCase() === currentShortcut.key.toLowerCase()) {
            keyDown = null;
          }
        }

        // Stop recording when shortcut is no longer pressed
        if (isRecording && !isShortcutPressed()) {
          stopRecording();
        }
      } catch (error) {
        console.error('❌ Error in keyup handler:', error);
      }
    });

    // Handle uiohook errors
    uIOhook.on('error', (error) => {
      console.error('❌ uIOhook error:', error);
      setTimeout(() => {
        console.log('🔄 Attempting to restart uiohook...');
        try {
          uIOhook.stop();
          setTimeout(() => {
            uIOhook.start();
          }, 500);
        } catch (restartError) {
          console.error('❌ Failed to restart uiohook:', restartError);
        }
      }, 1000);
    });

  } catch (error) {
    console.error('❌ Failed to register global shortcuts:', error);
    console.log('⚠️ Consider using Electron globalShortcut as fallback');
  }
}

// Helper function to check if current shortcut is pressed
function isShortcutPressed() {
  const shortcut = currentShortcut;

  // Check modifiers
  if (shortcut.ctrl && !ctrlDown) return false;
  if (shortcut.alt && !altDown) return false;
  if (shortcut.shift && !shiftDown) return false;

  // Check regular key (if specified)
  if (shortcut.key && !keyDown) return false;

  // Check that we don't have extra modifiers pressed
  if (!shortcut.ctrl && ctrlDown) return false;
  if (!shortcut.alt && altDown) return false;
  if (!shortcut.shift && shiftDown) return false;

  return true;
}

// Helper function to convert uiohook keycode to character
function getKeyFromCode(keycode) {
  // Basic character mapping for common keys
  const keyMap = {
    [UiohookKey.A]: 'a', [UiohookKey.B]: 'b', [UiohookKey.C]: 'c', [UiohookKey.D]: 'd',
    [UiohookKey.E]: 'e', [UiohookKey.F]: 'f', [UiohookKey.G]: 'g', [UiohookKey.H]: 'h',
    [UiohookKey.I]: 'i', [UiohookKey.J]: 'j', [UiohookKey.K]: 'k', [UiohookKey.L]: 'l',
    [UiohookKey.M]: 'm', [UiohookKey.N]: 'n', [UiohookKey.O]: 'o', [UiohookKey.P]: 'p',
    [UiohookKey.Q]: 'q', [UiohookKey.R]: 'r', [UiohookKey.S]: 's', [UiohookKey.T]: 't',
    [UiohookKey.U]: 'u', [UiohookKey.V]: 'v', [UiohookKey.W]: 'w', [UiohookKey.X]: 'x',
    [UiohookKey.Y]: 'y', [UiohookKey.Z]: 'z',
    [UiohookKey.Space]: ' ',
    [UiohookKey.Tab]: 'Tab',
    [UiohookKey.Enter]: 'Enter',
    [UiohookKey.Backspace]: 'Backspace',
    [UiohookKey.Delete]: 'Delete'
  };

  return keyMap[keycode] || null;
}

// Load saved shortcut configuration
function loadShortcutConfiguration() {
  try {
    const { app } = require('electron');
    const fs = require('fs');
    const path = require('path');

    const userDataPath = app.getPath('userData');
    const settingsPath = path.join(userDataPath, 'shortcut-settings.json');

    if (fs.existsSync(settingsPath)) {
      const savedShortcut = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
      currentShortcut = savedShortcut;
      console.log('✅ Loaded saved shortcut configuration:', currentShortcut);
    } else {
      console.log('📝 Using default shortcut configuration:', currentShortcut);
    }
  } catch (error) {
    console.error('❌ Error loading shortcut configuration:', error);
    // Keep default configuration
  }
}

// Helper functions to avoid ipcMain.invoke confusion
async function saveAudioFile(audioData) {
  try {
    const base64Data = audioData.replace(/^data:audio\/\w+;base64,/, '');
    const buffer = Buffer.from(base64Data, 'base64');
    const filePath = path.join(os.tmpdir(), `voicea-${Date.now()}.wav`);
    fs.writeFileSync(filePath, buffer);
    console.log('✅ Audio saved:', filePath);
    return filePath;
  } catch (error) {
    console.error('❌ Failed to save audio:', error);
    throw error;
  }
}

async function transcribeAudioFile(audioFilePath) {
  try {
    // Check if user is logged in before allowing transcription
    if (!currentUser || !currentUser.userId || !currentUser.isActive) {
      throw new Error('User not authenticated. Please log in to use transcription.');
    }

    // Check if user has reached weekly limit
    if (currentUser.weeklyTokens >= currentUser.weeklyLimit) {
      throw new Error(`Weekly limit reached (${currentUser.weeklyTokens}/${currentUser.weeklyLimit} tokens). Please upgrade your plan or wait for next week.`);
    }

    if (!openai) {
      throw new Error('OpenAI not initialized. Please check your API key.');
    }

    if (!fs.existsSync(audioFilePath)) {
      throw new Error('Audio file not found: ' + audioFilePath);
    }

    console.log('🔄 Transcribing audio...');
    const startTime = Date.now();

    const transcription = await openai.audio.transcriptions.create({
      file: fs.createReadStream(audioFilePath),
      model: 'whisper-1'
    });

    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;

    console.log('✅ Transcription completed:', transcription.text);
    console.log(`⚡ Transcription time: ${processingTime.toFixed(2)}s`);

    // Update performance statistics
    updatePerformanceStats(processingTime);

    // Store transcription data for later token processing
    transcription.processingTime = processingTime;
    transcription.audioFilePath = audioFilePath;

    // Send transcription result to main window for activity tracking
    if (mainWindow) {
      mainWindow.webContents.send('transcription-complete', {
        text: transcription.text,
        timestamp: new Date().toISOString(),
        processingTime: processingTime,
        type: transcription.text.trim() === '' ? 'silent' : 'transcription'
      });
    }

    return transcription.text;
  } catch (error) {
    console.error('❌ Transcription Error:', error);
    throw error;
  }
}

// Separate function to update token usage after transcription and pasting
async function updateTokenUsage(transcriptionText) {
  try {
    // Check if user is logged in
    if (!currentUser || !currentUser.userId) {
      console.warn('⚠️ Cannot update token usage: User not logged in');
      return;
    }

    // Estimate tokens used
    const estimatedTokens = tokenTracker ?
      tokenTracker.estimateTokensFromText(transcriptionText) :
      Math.ceil(transcriptionText.length / 4);

    console.log(`📊 Calculated tokens: ${estimatedTokens} for transcription: "${transcriptionText.substring(0, 50)}..."`);

    // Track token usage locally
    if (tokenTracker) {
      await tokenTracker.trackTokenUsage(estimatedTokens, {
        textLength: transcriptionText.length,
        model: 'whisper-1'
      });
    }

    // Update token usage via API
    if (currentUser && currentUser.userId) {
      try {
        console.log(`📊 Updating token usage via API: ${estimatedTokens} tokens for user ${currentUser.email} (ID: ${currentUser.userId})`);

        const response = await fetch('https://easyvoice.kambaaincorporation.in/apiv2/update-usage', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            user_id: currentUser.userId,
            tokens_used: estimatedTokens
          })
        });

        const responseData = await response.json();
        console.log('✅ Token usage API response:', responseData);

        if (response.ok) {
          console.log('✅ Token usage tracked successfully via API');

          // Update current user data with response if available
          if (responseData && responseData.tokensUsed !== undefined) {
            currentUser.tokensUsed = responseData.tokensUsed;
            console.log(`📊 Updated user total tokens: ${currentUser.tokensUsed}`);
          } else {
            currentUser.tokensUsed += estimatedTokens;
          }

          if (responseData && responseData.weeklyTokens !== undefined) {
            currentUser.weeklyTokens = responseData.weeklyTokens;
            console.log(`📊 Updated user weekly tokens: ${currentUser.weeklyTokens}`);
          } else {
            currentUser.weeklyTokens += estimatedTokens;
          }

          // Update token tracker
          if (tokenTracker) {
            tokenTracker.setCurrentUser(currentUser);
          }
        } else {
          console.warn('❌ Failed to track token usage via API:', responseData);
          // Still update locally as fallback
          currentUser.tokensUsed += estimatedTokens;
          currentUser.weeklyTokens += estimatedTokens;
        }
      } catch (error) {
        console.error('❌ Error updating token usage via API:', error);
        // Update locally as fallback
        currentUser.tokensUsed += estimatedTokens;
        currentUser.weeklyTokens += estimatedTokens;
      }
    }

    // Notify renderer about token usage
    if (mainWindow) {
      mainWindow.webContents.send('token-used', estimatedTokens);
    }

    console.log(`📊 Token processing completed: ${estimatedTokens} tokens`);
    return estimatedTokens;
  } catch (error) {
    console.error('❌ Error updating token usage:', error);
    throw error;
  }
}

async function pasteTextToClipboard(text) {
  try {
    // Get translation settings from main window
    let translationSettings = {};
    try {
      if (mainWindow) {
        translationSettings = await mainWindow.webContents.executeJavaScript(`
          (() => {
            try {
              return JSON.parse(localStorage.getItem('translationSettings') || '{}');
            } catch (e) {
              return {};
            }
          })()
        `);
      }
    } catch (error) {
      console.error('❌ Error getting translation settings:', error);
    }

    // Prepare options for formatEmailLikeText
    let options = {
      formatEmail: true,
      translate: translationSettings.enabled && translationSettings.language,
      targetLanguage: translationSettings.language
    };

    let processedText = text;

    // Format email-like content (with optional translation)
    if (options.formatEmail) {
      console.log('🔄 Processing text with options:', {
        translate: options.translate,
        targetLanguage: options.targetLanguage
      });
      processedText = await formatEmailLikeText(processedText, options);
    }

    clipboard.writeText(processedText);
    const { exec } = require('child_process');
    const platform = process.platform;

    return new Promise((resolve) => {
      if (platform === 'win32') {
        exec('powershell -command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait(\'^v\')"', (error) => {
          if (error) console.warn('⚠️ Paste command failed, text copied to clipboard');
          resolve(true);
        });
      } else if (platform === 'darwin') {
        exec('osascript -e "tell application \\"System Events\\" to keystroke \\"v\\" using command down"', (error) => {
          if (error) console.warn('⚠️ Paste command failed, text copied to clipboard');
          resolve(true);
        });
      } else {
        exec('xdotool key ctrl+v', (error) => {
          if (error) console.warn('⚠️ Paste command failed, text copied to clipboard');
          resolve(true);
        });
      }
    });
  } catch (err) {
    console.error('❌ Paste failed:', err);
    // Fallback: just copy to clipboard
    clipboard.writeText(processedText);
    return true;
  }
}





async function formatEmailLikeText(text, options = {}) {
  // Base system prompt for grammar and formatting
  let systemPrompt = `You are a transcription assistant. Take the raw audio text input and:

1. Correct grammar, punctuation, and sentence casing.
2. Keep all words exactly as spoken, even if they are uncommon, technical, or misspelled (e.g., "incorporation").
3. Do not change the meaning, content, tone, or intent.
4. Do not explain, define, or comment on any words.
5. Do not add any extra information, examples, or formatting beyond basic grammar, punctuation, and capitalization.
6. When the speaker says sequential numbers like "one," "two," "three" followed by items, format as a numbered list
Output only the corrected text.`;

  // Add translation instruction if enabled
  if (options.translate && options.targetLanguage) {
    const languageNames = {
      'es': 'Spanish',
      'fr': 'French',
      'de': 'German',
      'it': 'Italian',
      'pt': 'Portuguese',
      'ru': 'Russian',
      'ja': 'Japanese',
      'ko': 'Korean',
      'zh': 'Chinese (Simplified)',
      'ar': 'Arabic',
      'hi': 'Hindi',
      'th': 'Thai',
      'vi': 'Vietnamese',
      'nl': 'Dutch',
      'sv': 'Swedish',
      'da': 'Danish',
      'no': 'Norwegian',
      'fi': 'Finnish',
      'pl': 'Polish'
    };

    const targetLanguageName = languageNames[options.targetLanguage] || options.targetLanguage;
    systemPrompt += `translate the corrected text into fluent and natural detect the language and translate to  ${targetLanguageName}. with Punctuation and sentence casing give me only the text nothing else or explaination is required, Do not support with double quotes. If the Input text is in complete english do not translate`;
  }

  console.log(systemPrompt);

  const response = await openai.chat.completions.create({
    model: "gpt-4", // or "gpt-3.5-turbo"
    messages: [
      {
        role: "system",
        content: systemPrompt
      },
      {
        role: "user",
        content: text
      }
    ]
  });

  let result = response.choices[0].message.content.trim();

  // Remove surrounding double quotes if present
  if (result.startsWith('"') && result.endsWith('"')) {
    result = result.slice(1, -1);
  }

  return result;
}




async function translateToEnglish(text) {
  const response = await openai.chat.completions.create({
    model: "gpt-4", // or "gpt-3.5-turbo"
    messages: [
      {
        role: "system",
        content: "You are a professional translator. Translate the following message into fluent and natural English."
      },
      {
        role: "user",
        content: text
      }
    ]
  });

  return response.choices[0].message.content.trim();
}




// IPC Handlers
ipcMain.handle('save-audio', async (_, audioData) => {
  return await saveAudioFile(audioData);
});

ipcMain.handle('transcribe-audio', async (_, audioFilePath) => {
  return await transcribeAudioFile(audioFilePath);
});

ipcMain.handle('paste-text', async (_, text) => {
  return await pasteTextToClipboard(text);
});

// Authentication IPC Handlers
ipcMain.handle('save-auth', async (_, authData) => {
  if (authService) {
    const result = await authService.saveAuth(authData);
    if (result && authData.user) {
      currentUser = authData.user;
      shortcutsEnabled = true;
      if (tokenTracker) {
        tokenTracker.setCurrentUser(currentUser);
      }
      console.log('✅ User authenticated, shortcuts enabled for:', currentUser.username);
    }
    return result;
  }
  return false;
});

ipcMain.handle('get-saved-auth', async () => {
  if (authService) {
    return await authService.getSavedAuth();
  }
  return null;
});

// Logout IPC Handler
ipcMain.handle('logout', async () => {
  try {
    if (authService) {
      await authService.clearAuth();
      console.log('✅ User logged out successfully');
    }

    // Clear current user and disable shortcuts
    currentUser = null;
    shortcutsEnabled = false;

    // Clear token tracker
    if (tokenTracker) {
      tokenTracker.setCurrentUser(null);
    }

    console.log('❌ Shortcuts disabled - user logged out');
    return { success: true };
  } catch (error) {
    console.error('❌ Logout error:', error);
    return { success: false, error: error.message };
  }
});

// Cleanup for uninstall IPC Handler
ipcMain.handle('cleanup-for-uninstall', async () => {
  try {
    await cleanupForUninstall();
    return { success: true };
  } catch (error) {
    console.error('❌ Cleanup error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('clear-auth', async () => {
  if (authService) {
    const result = await authService.clearAuth();
    if (result) {
      currentUser = null;
      shortcutsEnabled = false;
      if (tokenTracker) {
        tokenTracker.setCurrentUser(null);
      }
      console.log('✅ User logged out, shortcuts disabled');
    }
    return result;
  }
  return false;
});

ipcMain.handle('verify-user', async (_, userData) => {
  if (authService) {
    return await authService.verifyUser(userData);
  }
  return false;
});

// Login IPC Handler
ipcMain.handle('login', async (_, email, password) => {
  if (authService) {
    const result = await authService.login(email, password);
    if (result.success) {
      currentUser = result.user;

      // Check if user is active before enabling shortcuts
      if (currentUser.isActive) {
        shortcutsEnabled = true;
        console.log('✅ Login successful for user:', currentUser.email);
        console.log('✅ Shortcuts enabled for active user');
      } else {
        shortcutsEnabled = false;
        console.log('⚠️ Login successful but user subscription expired:', currentUser.email);
        console.log('❌ Shortcuts disabled for inactive user');
      }

      if (tokenTracker) {
        tokenTracker.setCurrentUser(currentUser);
      }
    } else {
      shortcutsEnabled = false;
      console.log('❌ Login failed, shortcuts disabled');
    }
    return result;
  }
  return { success: false, message: 'Authentication service not available' };
});

// Token usage tracking IPC Handler
ipcMain.handle('update-token-usage', async (_, tokensUsed) => {
  try {
    if (!currentUser || !currentUser.userId) {
      throw new Error('User not authenticated');
    }

    console.log(`📊 Updating token usage: ${tokensUsed} tokens for user ${currentUser.email} (ID: ${currentUser.userId})`);

    const response = await fetch('https://easyvoice.kambaaincorporation.in/apiv2/update-usage', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        user_id: currentUser.userId,
        tokens_used: tokensUsed
      })
    });

    console.log('Token usage API response status:', response.status);
    const responseData = await response.json();
    console.log('Token usage API response:', responseData);

    if (response.ok) {
      console.log('✅ Token usage tracked successfully');

      // Update current user data with response if available
      if (responseData && responseData.tokensUsed !== undefined) {
        currentUser.tokensUsed = responseData.tokensUsed;
      } else {
        currentUser.tokensUsed += tokensUsed;
      }

      if (responseData && responseData.weeklyTokens !== undefined) {
        currentUser.weeklyTokens = responseData.weeklyTokens;
      } else {
        currentUser.weeklyTokens += tokensUsed;
      }

      // Update token tracker
      if (tokenTracker) {
        tokenTracker.setCurrentUser(currentUser);
      }

      return { success: true, user: currentUser };
    } else {
      console.warn('❌ Failed to track token usage:', responseData);
      // Still update locally as fallback
      currentUser.tokensUsed += tokensUsed;
      currentUser.weeklyTokens += tokensUsed;
      return { success: false, message: responseData.message || 'Failed to update usage', user: currentUser };
    }
  } catch (error) {
    console.error('❌ Error updating token usage:', error);
    // Update locally as fallback
    if (currentUser) {
      currentUser.tokensUsed += tokensUsed;
      currentUser.weeklyTokens += tokensUsed;
    }
    return { success: false, message: error.message, user: currentUser };
  }
});

// Token tracking IPC Handlers
ipcMain.handle('get-token-stats', async () => {
  if (tokenTracker) {
    return tokenTracker.getUsageStats();
  }
  return { total: 0, daily: 0, weekly: 0, monthly: 0, sessionsCount: 0 };
});

// Window management IPC Handlers
ipcMain.on('show-user-plan', () => {
  createUserPlanWindow();
});

ipcMain.on('show-recording-widget', () => {
  if (!recordingWindow) {
    createRecordingWindow();
  }
  recordingWindow.showInactive(); // Show without stealing focus
});

ipcMain.on('widget-manually-closed', () => {
  console.log('📱 Widget manually closed by user');
  if (recordingWindow) {
    recordingWindow.hide();
  }
});

ipcMain.handle('update-recording-shortcut', async (event, shortcutConfig) => {
  try {
    console.log('🎹 Updating recording shortcut:', shortcutConfig);

    // Stop current uiohook
    if (shortcutsEnabled) {
      uIOhook.stop();
    }

    // Update global shortcut configuration
    currentShortcut = shortcutConfig;

    // Save to file system
    const fs = require('fs');
    const path = require('path');
    const userDataPath = app.getPath('userData');
    const settingsPath = path.join(userDataPath, 'shortcut-settings.json');

    fs.writeFileSync(settingsPath, JSON.stringify(shortcutConfig, null, 2));
    console.log('💾 Shortcut configuration saved to file');

    // Restart uiohook with new configuration
    if (shortcutsEnabled) {
      registerGlobalShortcuts();
    }

    console.log('✅ Recording shortcut updated successfully');
    return { success: true };
  } catch (error) {
    console.error('❌ Error updating recording shortcut:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.on('minimize-to-tray', () => {
  if (mainWindow) {
    mainWindow.hide();
    isMainWindowVisible = false;
  }
});

ipcMain.handle('recording-complete', async (event, audioData) => {
  try {
    console.log('🔄 Processing recording...');

    // Save audio file
    const audioFilePath = await saveAudioFile(audioData);
    console.log('✅ Audio file saved');

    // Transcribe audio (without token update yet)
    console.log('🔄 Starting transcription...');
    const transcription = await transcribeAudioFile(audioFilePath);
    console.log('✅ Transcription completed');

    // Paste transcription to clipboard and wait for completion
    console.log('🔄 Pasting transcription to clipboard...');
    await pasteTextToClipboard(transcription);
    console.log('✅ Clipboard paste completed');

    // Hide recording window only after transcription and pasting are complete
    if (recordingWindow) {
      recordingWindow.hide();
      console.log('✅ Recording window hidden');
    }

    // Update token usage via API after hiding window
    console.log('🔄 Updating token usage...');
    await updateTokenUsage(transcription);
    console.log('✅ Token usage updated');

    // Clean up temp file asynchronously
    setImmediate(() => {
      try {
        fs.unlinkSync(audioFilePath);
        console.log('✅ Temp file cleaned up');
      } catch (e) {
        console.warn('⚠️ Could not delete temp file:', e.message);
      }
    });

    console.log('🎉 Recording processing complete - All steps finished in correct order');
    return transcription;
  } catch (error) {
    console.error('❌ Recording processing failed:', error);

    // Hide recording window on error
    if (recordingWindow) {
      recordingWindow.hide();
    }

    throw error;
  }
});

// Manual recording controls (for testing or UI buttons)
ipcMain.handle('manual-start-recording', () => {
  // Check if user is logged in before allowing manual recording
  if (!currentUser || !currentUser.userId || !currentUser.isActive) {
    console.log('❌ Manual recording blocked: User not logged in');
    return { success: false, error: 'Please log in to use recording features' };
  }

  if (!isRecording) {
    startRecording();
    return { success: true };
  }
  return { success: false, error: 'Recording already in progress' };
});

ipcMain.handle('manual-stop-recording', () => {
  if (isRecording) {
    stopRecording();
    return { success: true };
  }
  return { success: false, error: 'No recording in progress' };
});

function createTray() {
  try {
    // Use simple path resolution with fallback
    let iconPath = getAppPath(path.join('assets', 'icon.png'));

    // If icon doesn't exist, try alternative paths or use a default
    if (!fs.existsSync(iconPath)) {
      iconPath = getAppPath('icon.png');
      if (!fs.existsSync(iconPath)) {
        console.warn('⚠️ No icon found, tray will use default system icon');
        // Create a minimal 16x16 transparent icon as fallback
        iconPath = null;
      }
    }

    if (iconPath) {
      tray = new Tray(iconPath);
    } else {
      // Use a minimal approach - create tray without icon
      try {
        tray = new Tray(getAppPath(path.join('assets', 'icon.png')));
      } catch (error) {
        console.warn('⚠️ Could not create tray with icon, creating without icon');
        return; // Skip tray creation if icon fails
      }
    }
    
    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'VoiceA Status',
        enabled: false
      },
      {
        type: 'separator'
      },
      {
        label: 'Open Dashboard',
        click: () => {
          if (mainWindow) {
            if (mainWindow.isVisible()) {
              mainWindow.focus();
            } else {
              mainWindow.show();
              isMainWindowVisible = true;
            }
            console.log('📱 Dashboard opened from tray menu');
          }
        }
      },
      {
        type: 'separator'
      },
      {
        label: 'Show Recording Widget',
        click: () => {
          if (!recordingWindow) {
            createRecordingWindow();
          }
          recordingWindow.showInactive(); // Show without stealing focus
        }
      },
      {
        label: 'Hide Recording Widget',
        click: () => {
          if (recordingWindow) {
            recordingWindow.hide();
          }
        }
      },
      {
        type: 'separator'
      },
      {
        label: 'Test Cleanup (for uninstall)',
        click: async () => {
          const { dialog } = require('electron');
          const result = await dialog.showMessageBox(null, {
            type: 'question',
            buttons: ['Yes', 'Cancel'],
            defaultId: 1,
            title: 'Test Cleanup',
            message: 'This will test the cleanup process that runs during uninstall.\n\nThis will disable auto-start and stop processes. Continue?'
          });

          if (result.response === 0) {
            try {
              await cleanupForUninstall();
              dialog.showMessageBox(null, {
                type: 'info',
                title: 'Cleanup Complete',
                message: 'Cleanup test completed successfully!\n\nAuto-start has been disabled and processes cleaned up.'
              });
            } catch (error) {
              dialog.showErrorBox('Cleanup Error', `Cleanup failed: ${error.message}`);
            }
          }
        }
      },
      {
        label: 'Quit VoiceA',
        click: () => app.quit()
      }
    ]);
    
    tray.setToolTip('VoiceA - Voice Transcription Assistant\nHotkey: Ctrl+Alt (Hold)\nDouble-click to open dashboard');
    tray.setContextMenu(contextMenu);

    // Add double-click handler to open dashboard
    tray.on('double-click', () => {
      if (mainWindow) {
        if (mainWindow.isVisible()) {
          mainWindow.focus();
        } else {
          mainWindow.show();
          isMainWindowVisible = true;
        }
        console.log('📱 Dashboard opened from tray double-click');
      }
    });

    console.log('✅ System tray created');
  } catch (error) {
    console.warn('⚠️ Tray creation failed:', error);
  }
}

// App event handlers
app.whenReady().then(async () => {
  console.log('🚀 VoiceA starting...');

  // Initialize services first
  await initializeServices();

  // Load saved shortcut configuration
  loadShortcutConfiguration();

  createTray();
  createMainWindow();

  app.on('activate', function () {
    // On macOS, recreate a window in the app when the dock icon is clicked
    // and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });

  createRecordingWindow();
  registerGlobalShortcuts();
  console.log('✅ VoiceA ready! Use Ctrl+Alt to record.');
});

app.on('will-quit', async () => {
  console.log('🛑 Shutting down...');

  // Perform cleanup (but don't disable auto-launch during normal shutdown)
  // Stop recording if active
  if (isRecording) {
    stopRecording();
  }

  // Stop uiohook
  try {
    uIOhook.stop();
    console.log('✅ uIOhook stopped');
  } catch (error) {
    console.warn('⚠️ Error stopping uIOhook:', error);
  }

  // Clean up timeouts
  if (recordingTimeout) {
    clearTimeout(recordingTimeout);
  }
});

app.on('window-all-closed', () => {
  // On macOS, keep app running even if all windows are closed
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // On macOS, recreate window when dock icon is clicked
  if (BrowserWindow.getAllWindows().length === 0) {
    createRecordingWindow();
  }
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});

console.log('📱 VoiceA initialized - main.js loaded');