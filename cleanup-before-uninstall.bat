@echo off
echo Easy Voice - Cleanup Before Uninstall
echo =====================================
echo.
echo This script will stop all Easy Voice processes and remove auto-start configuration.
echo Run this before uninstalling Easy Voice for a clean removal.
echo.
pause

echo.
echo Stopping Easy Voice processes...
taskkill /F /IM "Easy Voice.exe" /T >nul 2>&1
taskkill /F /IM "EasyVoice.exe" /T >nul 2>&1
taskkill /F /IM "voicea.exe" /T >nul 2>&1

echo Waiting for processes to terminate...
timeout /t 3 /nobreak >nul

echo.
echo Running PowerShell cleanup script...
powershell.exe -ExecutionPolicy Bypass -File "installer\pre-uninstall.ps1"

echo.
echo Cleanup completed! You can now safely uninstall Easy Voice.
echo.
pause
