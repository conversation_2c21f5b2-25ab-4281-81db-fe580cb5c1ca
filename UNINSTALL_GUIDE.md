# Easy Voice - Uninstall Guide

This guide explains how to cleanly uninstall Easy Voice from your system.

## 🔄 Automatic Clean Uninstall (Recommended)

The Easy Voice installer includes automatic cleanup functionality that will:

1. **Stop all running processes** - Safely terminates all Easy Voice processes
2. **Remove auto-start configuration** - Removes startup entries from Windows registry
3. **Clean up shortcuts** - Removes desktop and start menu shortcuts
4. **Optional user data removal** - Asks if you want to remove settings and user data

### Steps:
1. Go to **Settings > Apps & Features** (Windows 10/11) or **Control Panel > Programs** (older Windows)
2. Find "Easy Voice" in the list
3. Click **Uninstall**
4. The uninstaller will automatically perform cleanup and ask about user data removal

## 🛠️ Manual Cleanup (If Needed)

If you need to manually clean up before uninstalling:

### Option 1: Run Cleanup Script
1. Navigate to the Easy Voice installation directory
2. Run `cleanup-before-uninstall.bat` as Administrator
3. Follow the prompts
4. Proceed with normal uninstallation

### Option 2: PowerShell Script
1. Open PowerShell as Administrator
2. Navigate to the Easy Voice installation directory
3. Run: `powershell.exe -ExecutionPolicy Bypass -File "installer\pre-uninstall.ps1"`
4. Follow the prompts

### Option 3: Manual Steps
If scripts don't work, you can manually:

1. **Close Easy Voice**:
   - Right-click the system tray icon and select "Quit"
   - Or use Task Manager to end all Easy Voice processes

2. **Remove Auto-Start**:
   - Press `Win + R`, type `msconfig`, press Enter
   - Go to "Startup" tab and disable Easy Voice entries
   - Or use Task Manager > Startup tab

3. **Clean Registry** (Advanced users only):
   - Press `Win + R`, type `regedit`, press Enter
   - Navigate to `HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Run`
   - Delete any Easy Voice, EasyVoice, or VoiceFlow entries

## 📁 User Data Locations

Easy Voice stores user data in these locations:

- `%APPDATA%\Easy Voice\` - Main application data
- `%APPDATA%\EasyVoice\` - Alternative data location
- `%APPDATA%\voicea\` - Legacy data location

The uninstaller will ask if you want to remove these directories.

## 🔍 Verification

After uninstalling, verify complete removal:

1. **Check Programs List**: Easy Voice should not appear in installed programs
2. **Check Startup**: No Easy Voice entries in startup programs
3. **Check System Tray**: No Easy Voice icon should appear after restart
4. **Check Installation Directory**: Default location should be removed

## ⚠️ Troubleshooting

### Uninstaller Won't Start
- Make sure Easy Voice is completely closed
- Run the uninstaller as Administrator
- Use the manual cleanup scripts first

### Processes Won't Stop
- Open Task Manager (Ctrl+Shift+Esc)
- End all processes containing "Easy Voice", "EasyVoice", or "voicea"
- Wait a few seconds before running uninstaller

### Auto-Start Still Active
- Check both current user and all users startup locations
- Use `msconfig` or Task Manager to disable startup entries
- Manually edit registry if necessary (advanced users only)

### User Data Remains
- Manually delete the folders listed in "User Data Locations" above
- Check for any remaining shortcuts on desktop or start menu

## 🆘 Support

If you encounter issues during uninstallation:

1. Try running the cleanup scripts as Administrator
2. Restart your computer and try again
3. Contact support with details about the specific error

## 🔄 Reinstallation

If you want to reinstall Easy Voice:

1. Complete the uninstallation process
2. Restart your computer (recommended)
3. Download the latest installer
4. Run the installer as Administrator

The new installation will be completely fresh with default settings.
