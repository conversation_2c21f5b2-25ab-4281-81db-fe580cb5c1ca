name: Build and Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

permissions:
  contents: write
  actions: read
  checks: write

jobs:
  build:
    runs-on: ${{ matrix.os }}
    
    strategy:
      matrix:
        os: [windows-latest, macos-latest]
        
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build for Windows
        if: matrix.os == 'windows-latest'
        run: npm run build:win
        env:
          ELECTRON_BUILDER_ALLOW_UNRESOLVED_DEPENDENCIES: true

      - name: Build for macOS
        if: matrix.os == 'macos-latest'
        run: npm run build:mac
        env:
          ELECTRON_BUILDER_ALLOW_UNRESOLVED_DEPENDENCIES: true
          
      - name: Upload Windows artifacts
        if: matrix.os == 'windows-latest'
        uses: actions/upload-artifact@v4
        with:
          name: windows-build
          path: dist/*.exe
          
      - name: Upload macOS artifacts
        if: matrix.os == 'macos-latest'
        uses: actions/upload-artifact@v4
        with:
          name: macos-build
          path: dist/*.zip

  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')

    steps:
      - name: Download Windows artifacts
        uses: actions/download-artifact@v4
        with:
          name: windows-build
          path: ./dist/

      - name: Download macOS artifacts
        uses: actions/download-artifact@v4
        with:
          name: macos-build
          path: ./dist/

      - name: List downloaded files
        run: |
          echo "Downloaded files:"
          ls -la ./dist/

      - name: Create Release
        uses: softprops/action-gh-release@v2
        with:
          files: ./dist/*
          draft: false
          prerelease: false
          generate_release_notes: true
